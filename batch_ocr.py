#!/usr/bin/env python3
"""
批量PDF OCR处理脚本
支持Docker和本地两种方式
"""

import os
import sys
import argparse
import concurrent.futures
from pathlib import Path
import time
from typing import List, Tuple
import json

# 导入我们的OCR处理器
try:
    from ocr_docker import OCRDockerProcessor
    DOCKER_AVAILABLE = True
except ImportError:
    DOCKER_AVAILABLE = False

try:
    from ocr_local import OCRLocalProcessor
    LOCAL_AVAILABLE = True
except ImportError:
    LOCAL_AVAILABLE = False


class BatchOCRProcessor:
    def __init__(self, method="docker", max_workers=2):
        """
        初始化批量OCR处理器
        
        Args:
            method: 处理方式 ("docker" 或 "local")
            max_workers: 最大并发工作线程数
        """
        self.method = method
        self.max_workers = max_workers
        
        if method == "docker":
            if not DOCKER_AVAILABLE:
                raise ImportError("Docker处理器不可用")
            self.processor = OCRDockerProcessor()
        elif method == "local":
            if not LOCAL_AVAILABLE:
                raise ImportError("本地处理器不可用")
            self.processor = OCRLocalProcessor()
            if not self.processor.check_dependencies():
                raise RuntimeError("本地依赖项检查失败")
        else:
            raise ValueError(f"不支持的处理方式: {method}")
    
    def find_pdf_files(self, input_dir: Path, recursive=True) -> List[Path]:
        """
        查找PDF文件
        
        Args:
            input_dir: 输入目录
            recursive: 是否递归查找
            
        Returns:
            PDF文件路径列表
        """
        if not input_dir.exists():
            raise FileNotFoundError(f"输入目录不存在: {input_dir}")
        
        if recursive:
            pdf_files = list(input_dir.rglob("*.pdf"))
        else:
            pdf_files = list(input_dir.glob("*.pdf"))
        
        # 过滤掉已经处理过的文件（以ocr_开头的文件）
        pdf_files = [f for f in pdf_files if not f.name.startswith("ocr_")]
        
        return sorted(pdf_files)
    
    def process_single_file(self, input_file: Path, output_file: Path, 
                          language="chi_sim+eng", **kwargs) -> Tuple[bool, str, float]:
        """
        处理单个PDF文件
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            language: OCR语言
            **kwargs: 其他参数
            
        Returns:
            (成功标志, 错误信息, 处理时间)
        """
        start_time = time.time()
        
        try:
            # 创建输出目录
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            if self.method == "docker":
                success = self.processor.ocr_pdf_stdin(
                    input_file, output_file, language, **kwargs
                )
            else:
                success = self.processor.ocr_pdf(
                    input_file, output_file, language, **kwargs
                )
            
            processing_time = time.time() - start_time
            
            if success:
                return True, "", processing_time
            else:
                return False, "OCR处理失败", processing_time
                
        except Exception as e:
            processing_time = time.time() - start_time
            return False, str(e), processing_time
    
    def batch_process(self, input_dir: Path, output_dir: Path, 
                     language="chi_sim+eng", recursive=True, 
                     preserve_structure=True, **kwargs):
        """
        批量处理PDF文件
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            language: OCR语言
            recursive: 是否递归查找
            preserve_structure: 是否保持目录结构
            **kwargs: 其他OCR参数
        """
        # 查找PDF文件
        pdf_files = self.find_pdf_files(input_dir, recursive)
        
        if not pdf_files:
            print(f"在 {input_dir} 中未找到PDF文件")
            return
        
        print(f"找到 {len(pdf_files)} 个PDF文件")
        print(f"使用方式: {self.method}")
        print(f"并发线程: {self.max_workers}")
        print(f"OCR语言: {language}")
        print("-" * 50)
        
        # 准备任务列表
        tasks = []
        for input_file in pdf_files:
            if preserve_structure:
                # 保持目录结构
                relative_path = input_file.relative_to(input_dir)
                output_file = output_dir / relative_path.parent / f"ocr_{relative_path.name}"
            else:
                # 所有文件放在输出目录根目录
                output_file = output_dir / f"ocr_{input_file.name}"
            
            tasks.append((input_file, output_file))
        
        # 执行批量处理
        results = []
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_task = {
                executor.submit(
                    self.process_single_file, 
                    input_file, output_file, language, **kwargs
                ): (input_file, output_file)
                for input_file, output_file in tasks
            }
            
            # 处理结果
            for i, future in enumerate(concurrent.futures.as_completed(future_to_task)):
                input_file, output_file = future_to_task[future]
                
                try:
                    success, error_msg, processing_time = future.result()
                    
                    result = {
                        "input_file": str(input_file),
                        "output_file": str(output_file),
                        "success": success,
                        "error": error_msg,
                        "processing_time": processing_time
                    }
                    results.append(result)
                    
                    status = "✅" if success else "❌"
                    print(f"{status} [{i+1}/{len(tasks)}] {input_file.name} "
                          f"({processing_time:.1f}s)")
                    
                    if not success and error_msg:
                        print(f"   错误: {error_msg}")
                
                except Exception as e:
                    print(f"❌ [{i+1}/{len(tasks)}] {input_file.name} - 异常: {e}")
                    results.append({
                        "input_file": str(input_file),
                        "output_file": str(output_file),
                        "success": False,
                        "error": str(e),
                        "processing_time": 0
                    })
        
        # 统计结果
        total_time = time.time() - start_time
        success_count = sum(1 for r in results if r["success"])
        
        print("-" * 50)
        print(f"批量处理完成!")
        print(f"总文件数: {len(results)}")
        print(f"成功处理: {success_count}")
        print(f"失败处理: {len(results) - success_count}")
        print(f"总耗时: {total_time:.1f}秒")
        print(f"平均耗时: {total_time/len(results):.1f}秒/文件")
        
        # 保存处理报告
        report_file = output_dir / "batch_ocr_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "summary": {
                    "total_files": len(results),
                    "success_count": success_count,
                    "failure_count": len(results) - success_count,
                    "total_time": total_time,
                    "method": self.method,
                    "language": language
                },
                "results": results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"处理报告已保存: {report_file}")


def main():
    parser = argparse.ArgumentParser(description="批量PDF OCR处理")
    parser.add_argument("input_dir", help="输入目录路径")
    parser.add_argument("output_dir", help="输出目录路径")
    parser.add_argument("-m", "--method", choices=["docker", "local"], 
                       default="docker", help="处理方式 (默认: docker)")
    parser.add_argument("-l", "--language", default="chi_sim+eng", 
                       help="OCR语言 (默认: chi_sim+eng)")
    parser.add_argument("-w", "--workers", type=int, default=2,
                       help="并发工作线程数 (默认: 2)")
    parser.add_argument("--no-recursive", action="store_true", 
                       help="不递归查找子目录")
    parser.add_argument("--flat-output", action="store_true",
                       help="不保持目录结构，所有输出文件放在根目录")
    parser.add_argument("--deskew", action="store_true", help="自动纠正倾斜")
    parser.add_argument("--clean", action="store_true", help="清理图像")
    parser.add_argument("--optimize", type=int, choices=[0, 1, 2, 3], default=1,
                       help="优化级别 (0-3, 默认: 1)")
    
    args = parser.parse_args()
    
    # 检查方法可用性
    if args.method == "docker" and not DOCKER_AVAILABLE:
        print("错误: Docker方式不可用，请检查ocr_docker.py")
        sys.exit(1)
    
    if args.method == "local" and not LOCAL_AVAILABLE:
        print("错误: 本地方式不可用，请检查ocr_local.py")
        sys.exit(1)
    
    # 准备参数
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    
    ocr_kwargs = {
        "deskew": args.deskew,
        "clean": args.clean,
        "optimize": args.optimize
    }
    
    try:
        # 创建批量处理器
        processor = BatchOCRProcessor(args.method, args.workers)
        
        # 执行批量处理
        processor.batch_process(
            input_dir=input_dir,
            output_dir=output_dir,
            language=args.language,
            recursive=not args.no_recursive,
            preserve_structure=not args.flat_output,
            **ocr_kwargs
        )
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
