#!/usr/bin/env python3
"""
OCRmyPDF 测试脚本
测试Docker和本地OCR功能是否正常工作
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path
import time

def test_docker_availability():
    """测试Docker是否可用"""
    print("🐳 测试Docker可用性...")
    try:
        result = subprocess.run(
            ["docker", "--version"], 
            capture_output=True, 
            text=True, 
            check=True
        )
        print(f"✅ Docker可用: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Docker不可用")
        return False

def test_docker_image():
    """测试OCRmyPDF Docker镜像"""
    print("\n🔍 测试OCRmyPDF Docker镜像...")
    try:
        # 检查镜像是否存在
        result = subprocess.run(
            ["docker", "images", "jbarlow83/ocrmypdf-alpine", "--format", "{{.Repository}}:{{.Tag}}"],
            capture_output=True,
            text=True
        )
        
        if "jbarlow83/ocrmypdf-alpine" in result.stdout:
            print("✅ OCRmyPDF镜像已存在")
        else:
            print("⚠️  OCRmyPDF镜像不存在，尝试拉取...")
            subprocess.run(["docker", "pull", "jbarlow83/ocrmypdf-alpine"], check=True)
            print("✅ OCRmyPDF镜像拉取成功")
        
        # 测试镜像版本
        result = subprocess.run(
            ["docker", "run", "--rm", "jbarlow83/ocrmypdf-alpine", "--version"],
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ OCRmyPDF版本: {result.stdout.strip()}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Docker镜像测试失败: {e}")
        return False

def test_local_dependencies():
    """测试本地依赖"""
    print("\n💻 测试本地依赖...")
    
    dependencies = {
        "tesseract": ["tesseract", "--version"],
        "ghostscript": ["gs", "--version"]
    }
    
    all_available = True
    
    for name, cmd in dependencies.items():
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            version_line = result.stdout.split('\n')[0] if result.stdout else result.stderr.split('\n')[0]
            print(f"✅ {name}: {version_line}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {name}: 未安装或不可用")
            all_available = False
    
    # 测试OCRmyPDF Python包
    try:
        import ocrmypdf
        print(f"✅ OCRmyPDF Python包: {ocrmypdf.__version__}")
    except ImportError:
        print("❌ OCRmyPDF Python包: 未安装")
        all_available = False
    
    return all_available

def create_test_pdf():
    """创建一个简单的测试PDF"""
    print("\n📄 创建测试PDF...")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        
        # 创建临时PDF文件
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        test_pdf = temp_dir / "test_input.pdf"
        
        c = canvas.Canvas(str(test_pdf), pagesize=letter)
        
        # 添加文本
        c.drawString(100, 750, "OCRmyPDF Test Document")
        c.drawString(100, 700, "This is a test PDF for OCR processing.")
        c.drawString(100, 650, "测试中文文字识别功能")
        c.drawString(100, 600, "English and Chinese mixed text")
        c.drawString(100, 550, "日本語のテスト")
        
        c.save()
        print(f"✅ 测试PDF创建成功: {test_pdf}")
        return test_pdf
        
    except ImportError:
        print("⚠️  reportlab未安装，使用简单文本文件代替")
        
        # 创建简单的文本文件作为替代
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        test_txt = temp_dir / "test_input.txt"
        
        with open(test_txt, 'w', encoding='utf-8') as f:
            f.write("OCRmyPDF Test Document\n")
            f.write("This is a test file for OCR processing.\n")
            f.write("测试中文文字识别功能\n")
            f.write("English and Chinese mixed text\n")
        
        print(f"✅ 测试文本文件创建成功: {test_txt}")
        return test_txt

def test_docker_ocr():
    """测试Docker OCR功能"""
    print("\n🔍 测试Docker OCR功能...")
    
    # 创建测试文件
    test_file = create_test_pdf()
    if not test_file.exists():
        print("❌ 测试文件创建失败")
        return False
    
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    output_file = output_dir / "test_docker_output.pdf"
    
    try:
        # 导入我们的Docker处理器
        from ocr_docker import OCRDockerProcessor
        
        processor = OCRDockerProcessor()
        start_time = time.time()
        
        success = processor.ocr_pdf_stdin(
            test_file, output_file, 
            language="chi_sim+eng",
            optimize=1
        )
        
        processing_time = time.time() - start_time
        
        if success and output_file.exists():
            file_size = output_file.stat().st_size
            print(f"✅ Docker OCR测试成功")
            print(f"   处理时间: {processing_time:.2f}秒")
            print(f"   输出文件: {output_file} ({file_size} bytes)")
            return True
        else:
            print("❌ Docker OCR测试失败")
            return False
            
    except Exception as e:
        print(f"❌ Docker OCR测试异常: {e}")
        return False

def test_local_ocr():
    """测试本地OCR功能"""
    print("\n🔍 测试本地OCR功能...")
    
    try:
        # 检查OCRmyPDF是否可用
        import ocrmypdf
    except ImportError:
        print("❌ OCRmyPDF未安装，跳过本地测试")
        return False
    
    # 创建测试文件
    test_file = create_test_pdf()
    if not test_file.exists():
        print("❌ 测试文件创建失败")
        return False
    
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    output_file = output_dir / "test_local_output.pdf"
    
    try:
        from ocr_local import OCRLocalProcessor
        
        processor = OCRLocalProcessor()
        
        # 检查依赖
        if not processor.check_dependencies():
            print("❌ 本地依赖检查失败")
            return False
        
        start_time = time.time()
        
        success = processor.ocr_pdf(
            test_file, output_file,
            language="chi_sim+eng",
            optimize=1
        )
        
        processing_time = time.time() - start_time
        
        if success and output_file.exists():
            file_size = output_file.stat().st_size
            print(f"✅ 本地OCR测试成功")
            print(f"   处理时间: {processing_time:.2f}秒")
            print(f"   输出文件: {output_file} ({file_size} bytes)")
            return True
        else:
            print("❌ 本地OCR测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 本地OCR测试异常: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    test_files = [
        "temp/test_input.pdf",
        "temp/test_input.txt",
        "output/test_docker_output.pdf",
        "output/test_local_output.pdf"
    ]
    
    for file_path in test_files:
        path = Path(file_path)
        if path.exists():
            path.unlink()
            print(f"   删除: {file_path}")

def main():
    """主测试函数"""
    print("🧪 OCRmyPDF 功能测试")
    print("=" * 50)
    
    # 创建必要目录
    for dir_name in ["temp", "output"]:
        Path(dir_name).mkdir(exist_ok=True)
    
    test_results = {}
    
    # 测试Docker
    test_results["docker_available"] = test_docker_availability()
    if test_results["docker_available"]:
        test_results["docker_image"] = test_docker_image()
        if test_results["docker_image"]:
            test_results["docker_ocr"] = test_docker_ocr()
    
    # 测试本地环境
    test_results["local_deps"] = test_local_dependencies()
    if test_results["local_deps"]:
        test_results["local_ocr"] = test_local_ocr()
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
    
    # 给出建议
    print("\n💡 建议:")
    if test_results.get("docker_ocr"):
        print("✅ Docker方式可用，推荐使用 ocr_docker.py")
    elif test_results.get("local_ocr"):
        print("✅ 本地方式可用，可以使用 ocr_local.py")
    else:
        print("❌ 两种方式都不可用，请检查安装")
        print("   - Docker方式: 安装Docker并拉取镜像")
        print("   - 本地方式: 安装tesseract、ghostscript和ocrmypdf")
    
    # 询问是否清理测试文件
    try:
        response = input("\n是否清理测试文件? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            cleanup_test_files()
    except KeyboardInterrupt:
        print("\n测试完成")

if __name__ == "__main__":
    main()
