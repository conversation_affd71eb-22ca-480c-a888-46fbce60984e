#!/usr/bin/env python3
"""
OCRmyPDF Docker 脚本
使用Docker镜像进行PDF OCR识别
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import tempfile
import shutil


class OCRDockerProcessor:
    def __init__(self, docker_image="jbarlow83/ocrmypdf-alpine"):
        """
        初始化OCR Docker处理器
        
        Args:
            docker_image: Docker镜像名称
        """
        self.docker_image = docker_image
        self.check_docker()
    
    def check_docker(self):
        """检查Docker是否可用"""
        try:
            result = subprocess.run(
                ["docker", "--version"], 
                capture_output=True, 
                text=True, 
                check=True
            )
            print(f"Docker已就绪: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("错误: Docker未安装或不可用")
            print("请先安装Docker: https://docs.docker.com/get-docker/")
            sys.exit(1)
    
    def pull_image(self):
        """拉取OCRmyPDF Docker镜像"""
        print(f"正在拉取Docker镜像: {self.docker_image}")
        try:
            subprocess.run(
                ["docker", "pull", self.docker_image], 
                check=True
            )
            print("镜像拉取成功!")
        except subprocess.CalledProcessError as e:
            print(f"镜像拉取失败: {e}")
            sys.exit(1)
    
    def ocr_pdf_stdin(self, input_path, output_path, language="chi_sim+eng", **kwargs):
        """
        使用stdin/stdout方式进行OCR处理（推荐方式，避免权限问题）
        
        Args:
            input_path: 输入PDF文件路径
            output_path: 输出PDF文件路径
            language: OCR语言，默认中文+英文
            **kwargs: 其他OCRmyPDF参数
        """
        input_path = Path(input_path)
        output_path = Path(output_path)
        
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_path}")
        
        # 构建Docker命令
        cmd = [
            "docker", "run", "--rm", "-i",
            self.docker_image,
            "--language", language,
            "--output-type", "pdf"
        ]
        
        # 添加其他参数
        for key, value in kwargs.items():
            if value is True:
                cmd.append(f"--{key.replace('_', '-')}")
            elif value is not False and value is not None:
                cmd.extend([f"--{key.replace('_', '-')}", str(value)])
        
        cmd.extend(["-", "-"])  # stdin to stdout
        
        print(f"正在处理: {input_path} -> {output_path}")
        print(f"使用语言: {language}")
        
        try:
            with open(input_path, 'rb') as input_file:
                with open(output_path, 'wb') as output_file:
                    result = subprocess.run(
                        cmd,
                        stdin=input_file,
                        stdout=output_file,
                        stderr=subprocess.PIPE,
                        text=False
                    )
            
            if result.returncode == 0:
                print(f"OCR处理完成: {output_path}")
                return True
            else:
                error_msg = result.stderr.decode('utf-8') if result.stderr else "未知错误"
                print(f"OCR处理失败: {error_msg}")
                return False
                
        except Exception as e:
            print(f"处理过程中出错: {e}")
            return False
    
    def ocr_pdf_volume(self, input_path, output_path, language="chi_sim+eng", **kwargs):
        """
        使用Docker volume方式进行OCR处理
        
        Args:
            input_path: 输入PDF文件路径
            output_path: 输出PDF文件路径
            language: OCR语言，默认中文+英文
            **kwargs: 其他OCRmyPDF参数
        """
        input_path = Path(input_path).resolve()
        output_path = Path(output_path).resolve()
        
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_path}")
        
        # 创建输出目录
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 获取当前用户ID（Linux/Mac）
        try:
            import pwd
            uid = os.getuid()
            gid = os.getgid()
            user_param = f"{uid}:{gid}"
        except (ImportError, AttributeError):
            # Windows系统
            user_param = "1000:1000"
        
        # 构建Docker命令
        cmd = [
            "docker", "run", "--rm",
            "--user", user_param,
            "--workdir", "/data",
            "-v", f"{input_path.parent}:/data",
            self.docker_image,
            "--language", language
        ]
        
        # 添加其他参数
        for key, value in kwargs.items():
            if value is True:
                cmd.append(f"--{key.replace('_', '-')}")
            elif value is not False and value is not None:
                cmd.extend([f"--{key.replace('_', '-')}", str(value)])
        
        cmd.extend([f"/data/{input_path.name}", f"/data/{output_path.name}"])
        
        print(f"正在处理: {input_path} -> {output_path}")
        print(f"使用语言: {language}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"OCR处理完成: {output_path}")
                return True
            else:
                print(f"OCR处理失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"处理过程中出错: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(description="使用Docker进行PDF OCR识别")
    parser.add_argument("input", help="输入PDF文件路径")
    parser.add_argument("output", help="输出PDF文件路径")
    parser.add_argument("-l", "--language", default="chi_sim+eng", 
                       help="OCR语言 (默认: chi_sim+eng)")
    parser.add_argument("--method", choices=["stdin", "volume"], default="stdin",
                       help="处理方式 (默认: stdin)")
    parser.add_argument("--deskew", action="store_true", help="自动纠正倾斜")
    parser.add_argument("--clean", action="store_true", help="清理图像")
    parser.add_argument("--optimize", type=int, choices=[0, 1, 2, 3], default=1,
                       help="优化级别 (0-3, 默认: 1)")
    parser.add_argument("--pull", action="store_true", help="拉取最新Docker镜像")
    
    args = parser.parse_args()
    
    # 创建处理器
    processor = OCRDockerProcessor()
    
    # 拉取镜像（如果需要）
    if args.pull:
        processor.pull_image()
    
    # 准备OCR参数
    ocr_kwargs = {
        "deskew": args.deskew,
        "clean": args.clean,
        "optimize": args.optimize
    }
    
    # 执行OCR处理
    if args.method == "stdin":
        success = processor.ocr_pdf_stdin(
            args.input, args.output, args.language, **ocr_kwargs
        )
    else:
        success = processor.ocr_pdf_volume(
            args.input, args.output, args.language, **ocr_kwargs
        )
    
    if success:
        print("✅ OCR处理成功完成!")
        sys.exit(0)
    else:
        print("❌ OCR处理失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
