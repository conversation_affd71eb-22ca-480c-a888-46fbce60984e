#!/usr/bin/env python3
"""
OCRmyPDF 启动脚本
自动选择最佳的OCR方式并提供简化的命令行界面
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def check_docker():
    """检查Docker是否可用"""
    try:
        subprocess.run(["docker", "--version"], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def check_local():
    """检查本地OCRmyPDF是否可用"""
    try:
        import ocrmypdf
        # 简单检查tesseract
        subprocess.run(["tesseract", "--version"], capture_output=True, check=True)
        return True
    except (ImportError, subprocess.CalledProcessError, FileNotFoundError):
        return False

def auto_detect_method():
    """自动检测可用的OCR方式"""
    docker_available = check_docker()
    local_available = check_local()
    
    if docker_available and local_available:
        return "docker", "Docker和本地方式都可用，优先使用Docker"
    elif docker_available:
        return "docker", "仅Docker方式可用"
    elif local_available:
        return "local", "仅本地方式可用"
    else:
        return None, "Docker和本地方式都不可用"

def run_docker_ocr(input_path, output_path, **kwargs):
    """运行Docker OCR"""
    cmd = ["python", "ocr_docker.py", str(input_path), str(output_path)]
    
    # 添加参数
    if kwargs.get("language"):
        cmd.extend(["-l", kwargs["language"]])
    if kwargs.get("deskew"):
        cmd.append("--deskew")
    if kwargs.get("clean"):
        cmd.append("--clean")
    if kwargs.get("optimize") is not None:
        cmd.extend(["--optimize", str(kwargs["optimize"])])
    
    return subprocess.run(cmd)

def run_local_ocr(input_path, output_path, **kwargs):
    """运行本地OCR"""
    cmd = ["python", "ocr_local.py", str(input_path), str(output_path)]
    
    # 添加参数
    if kwargs.get("language"):
        cmd.extend(["-l", kwargs["language"]])
    if kwargs.get("deskew"):
        cmd.append("--deskew")
    if kwargs.get("clean"):
        cmd.append("--clean")
    if kwargs.get("optimize") is not None:
        cmd.extend(["--optimize", str(kwargs["optimize"])])
    if kwargs.get("batch"):
        cmd.append("--batch")
    
    return subprocess.run(cmd)

def run_batch_ocr(input_dir, output_dir, method, **kwargs):
    """运行批量OCR"""
    cmd = ["python", "batch_ocr.py", str(input_dir), str(output_dir), "-m", method]
    
    # 添加参数
    if kwargs.get("language"):
        cmd.extend(["-l", kwargs["language"]])
    if kwargs.get("workers"):
        cmd.extend(["-w", str(kwargs["workers"])])
    if kwargs.get("deskew"):
        cmd.append("--deskew")
    if kwargs.get("clean"):
        cmd.append("--clean")
    if kwargs.get("optimize") is not None:
        cmd.extend(["--optimize", str(kwargs["optimize"])])
    if kwargs.get("no_recursive"):
        cmd.append("--no-recursive")
    if kwargs.get("flat_output"):
        cmd.append("--flat-output")
    
    return subprocess.run(cmd)

def interactive_mode():
    """交互式模式"""
    print("🔍 OCRmyPDF 交互式模式")
    print("=" * 40)
    
    # 检测可用方式
    method, message = auto_detect_method()
    print(f"状态: {message}")
    
    if not method:
        print("\n❌ 无可用的OCR方式，请先安装Docker或本地依赖")
        return
    
    print(f"将使用: {method} 方式")
    
    # 获取输入
    try:
        input_path = input("\n📁 输入PDF文件或目录路径: ").strip()
        if not input_path:
            print("❌ 输入路径不能为空")
            return
        
        input_path = Path(input_path)
        if not input_path.exists():
            print(f"❌ 路径不存在: {input_path}")
            return
        
        # 判断是文件还是目录
        is_batch = input_path.is_dir()
        
        if is_batch:
            output_path = input("📁 输出目录路径: ").strip()
            if not output_path:
                output_path = "output"
        else:
            output_path = input("📄 输出PDF文件路径: ").strip()
            if not output_path:
                output_path = f"ocr_{input_path.name}"
        
        output_path = Path(output_path)
        
        # 语言选择
        print("\n🌍 语言选择:")
        print("1. 中英文 (chi_sim+eng) [默认]")
        print("2. 仅中文 (chi_sim)")
        print("3. 仅英文 (eng)")
        print("4. 自定义")
        
        lang_choice = input("选择语言 (1-4): ").strip()
        
        language_map = {
            "1": "chi_sim+eng",
            "2": "chi_sim", 
            "3": "eng",
            "": "chi_sim+eng"  # 默认
        }
        
        if lang_choice in language_map:
            language = language_map[lang_choice]
        elif lang_choice == "4":
            language = input("输入语言代码 (如 chi_sim+eng): ").strip()
            if not language:
                language = "chi_sim+eng"
        else:
            language = "chi_sim+eng"
        
        # 高级选项
        print("\n⚙️ 高级选项 (y/N):")
        deskew = input("自动纠正倾斜? ").strip().lower() in ['y', 'yes']
        clean = input("清理图像噪声? ").strip().lower() in ['y', 'yes']
        
        optimize_input = input("优化级别 (0-3, 默认1): ").strip()
        try:
            optimize = int(optimize_input) if optimize_input else 1
            optimize = max(0, min(3, optimize))
        except ValueError:
            optimize = 1
        
        # 批量处理选项
        workers = 2
        if is_batch:
            workers_input = input("并发线程数 (默认2): ").strip()
            try:
                workers = int(workers_input) if workers_input else 2
                workers = max(1, min(8, workers))
            except ValueError:
                workers = 2
        
        # 执行OCR
        print(f"\n🚀 开始处理...")
        print(f"输入: {input_path}")
        print(f"输出: {output_path}")
        print(f"语言: {language}")
        print(f"方式: {method}")
        
        kwargs = {
            "language": language,
            "deskew": deskew,
            "clean": clean,
            "optimize": optimize
        }
        
        if is_batch:
            kwargs["workers"] = workers
            result = run_batch_ocr(input_path, output_path, method, **kwargs)
        else:
            if method == "docker":
                result = run_docker_ocr(input_path, output_path, **kwargs)
            else:
                result = run_local_ocr(input_path, output_path, **kwargs)
        
        if result.returncode == 0:
            print("✅ 处理完成!")
        else:
            print("❌ 处理失败!")
    
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")

def main():
    parser = argparse.ArgumentParser(description="OCRmyPDF 统一启动脚本")
    parser.add_argument("input", nargs="?", help="输入PDF文件或目录路径")
    parser.add_argument("output", nargs="?", help="输出PDF文件或目录路径")
    parser.add_argument("-m", "--method", choices=["auto", "docker", "local"], 
                       default="auto", help="OCR方式 (默认: auto)")
    parser.add_argument("-l", "--language", default="chi_sim+eng", 
                       help="OCR语言 (默认: chi_sim+eng)")
    parser.add_argument("-w", "--workers", type=int, default=2,
                       help="批量处理并发数 (默认: 2)")
    parser.add_argument("--deskew", action="store_true", help="自动纠正倾斜")
    parser.add_argument("--clean", action="store_true", help="清理图像")
    parser.add_argument("--optimize", type=int, choices=[0, 1, 2, 3], default=1,
                       help="优化级别 (0-3, 默认: 1)")
    parser.add_argument("--batch", action="store_true", help="强制批量模式")
    parser.add_argument("--interactive", "-i", action="store_true", help="交互式模式")
    parser.add_argument("--test", action="store_true", help="运行测试")
    parser.add_argument("--check", action="store_true", help="检查环境")
    
    args = parser.parse_args()
    
    # 特殊模式
    if args.test:
        subprocess.run(["python", "test_ocr.py"])
        return
    
    if args.check:
        method, message = auto_detect_method()
        print(f"环境检查: {message}")
        if method:
            print(f"推荐使用: {method} 方式")
        return
    
    if args.interactive or (not args.input):
        interactive_mode()
        return
    
    # 命令行模式
    input_path = Path(args.input)
    if not input_path.exists():
        print(f"❌ 输入路径不存在: {input_path}")
        sys.exit(1)
    
    if not args.output:
        if input_path.is_dir():
            output_path = Path("output")
        else:
            output_path = Path(f"ocr_{input_path.name}")
    else:
        output_path = Path(args.output)
    
    # 确定方式
    if args.method == "auto":
        method, message = auto_detect_method()
        if not method:
            print(f"❌ {message}")
            sys.exit(1)
        print(f"自动选择: {method}")
    else:
        method = args.method
    
    # 准备参数
    kwargs = {
        "language": args.language,
        "deskew": args.deskew,
        "clean": args.clean,
        "optimize": args.optimize
    }
    
    # 执行处理
    is_batch = input_path.is_dir() or args.batch
    
    if is_batch:
        kwargs["workers"] = args.workers
        result = run_batch_ocr(input_path, output_path, method, **kwargs)
    else:
        if method == "docker":
            result = run_docker_ocr(input_path, output_path, **kwargs)
        else:
            result = run_local_ocr(input_path, output_path, **kwargs)
    
    sys.exit(result.returncode)

if __name__ == "__main__":
    main()
