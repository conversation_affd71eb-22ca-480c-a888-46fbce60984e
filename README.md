# OCR PDF 文字识别工具

这是一个基于OCRmyPDF的PDF文字识别工具集，支持Docker和本地两种运行方式。

## 🚀 快速开始

### 方式一：Docker（推荐）

Docker方式无需安装复杂依赖，开箱即用。

#### 1. 安装Docker
```bash
# 检查Docker是否已安装
docker --version

# 如果未安装，请访问 https://docs.docker.com/get-docker/
```

#### 2. 拉取OCRmyPDF镜像
```bash
python ocr_docker.py --pull
```

#### 3. 处理单个PDF文件
```bash
# 基本用法（中英文识别）
python ocr_docker.py input.pdf output.pdf

# 指定语言
python ocr_docker.py input.pdf output.pdf -l chi_sim

# 添加图像处理选项
python ocr_docker.py input.pdf output.pdf --deskew --clean --optimize 2
```

### 方式二：本地安装

#### 1. 安装系统依赖

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install tesseract-ocr tesseract-ocr-chi-sim tesseract-ocr-chi-tra ghostscript
```

**CentOS/RHEL:**
```bash
sudo yum install tesseract tesseract-langpack-chi-sim ghostscript
```

**macOS:**
```bash
brew install tesseract tesseract-lang ghostscript
```

**Windows:**
请参考 [OCRmyPDF官方文档](https://ocrmypdf.readthedocs.io/en/latest/installation.html#windows)

#### 2. 安装Python依赖
```bash
pip install -r requirements.txt
```

#### 3. 检查依赖
```bash
python ocr_local.py --check-deps
```

#### 4. 处理PDF文件
```bash
# 基本用法
python ocr_local.py input.pdf output.pdf

# 查看可用语言
python ocr_local.py --list-languages

# 批量处理
python ocr_local.py input_folder output_folder --batch
```

## 📁 批量处理

使用 `batch_ocr.py` 可以批量处理多个PDF文件：

```bash
# Docker方式批量处理
python batch_ocr.py input_folder output_folder -m docker

# 本地方式批量处理
python batch_ocr.py input_folder output_folder -m local

# 高级选项
python batch_ocr.py input_folder output_folder \
    -m docker \
    -l chi_sim+eng \
    -w 4 \
    --deskew \
    --clean \
    --optimize 2
```

### 批量处理选项说明

- `-m, --method`: 处理方式 (`docker` 或 `local`)
- `-l, --language`: OCR语言 (默认: `chi_sim+eng`)
- `-w, --workers`: 并发线程数 (默认: 2)
- `--no-recursive`: 不递归查找子目录
- `--flat-output`: 不保持目录结构
- `--deskew`: 自动纠正倾斜
- `--clean`: 清理图像噪声
- `--optimize`: 优化级别 (0-3)

## 🐳 Docker Compose

项目包含 `docker-compose.yml` 文件，可以更方便地管理Docker服务：

```bash
# 创建必要的目录
mkdir -p input output temp

# 拉取镜像
docker-compose pull

# 使用Docker Compose运行（示例）
docker-compose run --rm ocrmypdf --language chi_sim+eng /data/input/test.pdf /data/output/test_ocr.pdf
```

## 🌍 支持的语言

常用语言代码：
- `eng`: 英文
- `chi_sim`: 简体中文
- `chi_tra`: 繁体中文
- `jpn`: 日文
- `kor`: 韩文
- `fra`: 法文
- `deu`: 德文
- `spa`: 西班牙文

可以组合多种语言，用 `+` 连接：
```bash
python ocr_docker.py input.pdf output.pdf -l chi_sim+eng+jpn
```

查看所有可用语言：
```bash
# Docker方式
docker run --rm jbarlow83/ocrmypdf-alpine --list-langs

# 本地方式
python ocr_local.py --list-languages
```

## ⚙️ 高级选项

### 图像处理选项

- `--deskew`: 自动纠正页面倾斜
- `--clean`: 清理图像噪声
- `--remove-background`: 移除背景
- `--threshold`: 二值化处理

### 优化选项

- `--optimize 0`: 不优化
- `--optimize 1`: 无损优化（默认）
- `--optimize 2`: 有损优化
- `--optimize 3`: 激进优化

### 输出格式

- `--output-type pdf`: PDF格式（默认）
- `--output-type pdfa`: PDF/A格式
- `--output-type pdfa-1`: PDF/A-1格式
- `--output-type pdfa-2`: PDF/A-2格式
- `--output-type pdfa-3`: PDF/A-3格式

## 📊 性能建议

1. **Docker vs 本地**: Docker方式更简单，本地方式性能更好
2. **并发处理**: 批量处理时可以调整 `-w` 参数增加并发数
3. **内存使用**: 大文件处理时注意内存使用，可以减少并发数
4. **语言选择**: 只选择需要的语言可以提高识别速度和准确性

## 🔧 故障排除

### Docker相关问题

1. **权限问题**: 
   ```bash
   # Linux/Mac用户可能需要调整用户权限
   sudo chown -R $USER:$USER output/
   ```

2. **内存不足**:
   ```bash
   # 增加Docker内存限制
   docker run --memory=4g --rm -i jbarlow83/ocrmypdf-alpine ...
   ```

### 本地安装问题

1. **Tesseract未找到**:
   ```bash
   # 检查Tesseract安装
   tesseract --version
   tesseract --list-langs
   ```

2. **语言包缺失**:
   ```bash
   # Ubuntu/Debian安装中文语言包
   sudo apt install tesseract-ocr-chi-sim tesseract-ocr-chi-tra
   ```

3. **Ghostscript问题**:
   ```bash
   # 检查Ghostscript
   gs --version
   ```

## 📝 示例

### 处理扫描的中文PDF
```bash
python ocr_docker.py scan.pdf output.pdf -l chi_sim --deskew --clean
```

### 批量处理发票PDF
```bash
python batch_ocr.py invoices/ processed/ -m docker -l chi_sim+eng --optimize 2
```

### 生成PDF/A格式
```bash
python ocr_docker.py input.pdf output.pdf --output-type pdfa-2
```

## 📄 许可证

本项目基于MIT许可证开源。OCRmyPDF本身基于MPL 2.0许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📚 相关链接

- [OCRmyPDF官方文档](https://ocrmypdf.readthedocs.io/)
- [Tesseract OCR](https://github.com/tesseract-ocr/tesseract)
- [Docker官网](https://www.docker.com/)
