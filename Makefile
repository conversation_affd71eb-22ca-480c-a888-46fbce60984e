# OCRmyPDF Makefile
# 提供常用命令的快捷方式

.PHONY: help setup test clean docker-pull install-local example

# 默认目标
help:
	@echo "OCRmyPDF PDF文字识别工具"
	@echo "=========================="
	@echo ""
	@echo "可用命令:"
	@echo "  help          - 显示此帮助信息"
	@echo "  setup         - 初始化项目环境"
	@echo "  test          - 运行功能测试"
	@echo "  docker-pull   - 拉取Docker镜像"
	@echo "  install-local - 安装本地依赖"
	@echo "  example       - 显示使用示例"
	@echo "  clean         - 清理临时文件"
	@echo "  interactive   - 启动交互式模式"
	@echo ""
	@echo "使用示例:"
	@echo "  make setup              # 初始化环境"
	@echo "  make test               # 测试功能"
	@echo "  make docker-pull        # 拉取Docker镜像"
	@echo "  python run_ocr.py -i   # 交互式使用"

# 初始化项目环境
setup:
	@echo "🚀 初始化OCRmyPDF项目环境..."
	@mkdir -p input output temp
	@echo "✅ 创建目录: input/, output/, temp/"
	@chmod +x *.py
	@echo "✅ 设置脚本执行权限"
	@echo ""
	@echo "下一步:"
	@echo "  - Docker方式: make docker-pull"
	@echo "  - 本地方式: make install-local"
	@echo "  - 运行测试: make test"

# 拉取Docker镜像
docker-pull:
	@echo "🐳 拉取OCRmyPDF Docker镜像..."
	docker pull jbarlow83/ocrmypdf-alpine:latest
	@echo "✅ Docker镜像拉取完成"

# 安装本地依赖
install-local:
	@echo "💻 安装本地Python依赖..."
	pip install -r requirements.txt
	@echo ""
	@echo "⚠️  还需要安装系统依赖:"
	@echo "Ubuntu/Debian: sudo apt install tesseract-ocr tesseract-ocr-chi-sim ghostscript"
	@echo "CentOS/RHEL:   sudo yum install tesseract tesseract-langpack-chi-sim ghostscript"
	@echo "macOS:         brew install tesseract tesseract-lang ghostscript"

# 运行测试
test:
	@echo "🧪 运行OCRmyPDF功能测试..."
	python test_ocr.py

# 显示使用示例
example:
	@echo "📚 OCRmyPDF使用示例..."
	python example.py

# 交互式模式
interactive:
	@echo "🔍 启动交互式模式..."
	python run_ocr.py -i

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	@rm -rf temp/* output/* __pycache__ *.pyc
	@find . -name "*.pyc" -delete
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@echo "✅ 清理完成"

# 检查环境
check:
	@echo "🔍 检查环境状态..."
	python run_ocr.py --check

# Docker方式处理单个文件
docker-single:
	@echo "🐳 Docker方式处理示例..."
	@echo "用法: make docker-single INPUT=input.pdf OUTPUT=output.pdf"
	@if [ -z "$(INPUT)" ] || [ -z "$(OUTPUT)" ]; then \
		echo "❌ 请指定INPUT和OUTPUT参数"; \
		echo "示例: make docker-single INPUT=test.pdf OUTPUT=result.pdf"; \
	else \
		python ocr_docker.py $(INPUT) $(OUTPUT); \
	fi

# 本地方式处理单个文件
local-single:
	@echo "💻 本地方式处理示例..."
	@echo "用法: make local-single INPUT=input.pdf OUTPUT=output.pdf"
	@if [ -z "$(INPUT)" ] || [ -z "$(OUTPUT)" ]; then \
		echo "❌ 请指定INPUT和OUTPUT参数"; \
		echo "示例: make local-single INPUT=test.pdf OUTPUT=result.pdf"; \
	else \
		python ocr_local.py $(INPUT) $(OUTPUT); \
	fi

# 批量处理
batch:
	@echo "📁 批量处理示例..."
	@echo "用法: make batch INPUT_DIR=input/ OUTPUT_DIR=output/ METHOD=docker"
	@if [ -z "$(INPUT_DIR)" ] || [ -z "$(OUTPUT_DIR)" ]; then \
		echo "❌ 请指定INPUT_DIR和OUTPUT_DIR参数"; \
		echo "示例: make batch INPUT_DIR=pdfs/ OUTPUT_DIR=results/ METHOD=docker"; \
	else \
		python batch_ocr.py $(INPUT_DIR) $(OUTPUT_DIR) -m $(or $(METHOD),docker); \
	fi

# 安装开发依赖
dev-install:
	@echo "🛠️ 安装开发依赖..."
	pip install reportlab pytest black flake8
	@echo "✅ 开发依赖安装完成"

# 代码格式化
format:
	@echo "🎨 格式化Python代码..."
	black *.py
	@echo "✅ 代码格式化完成"

# 代码检查
lint:
	@echo "🔍 检查代码质量..."
	flake8 *.py --max-line-length=88 --ignore=E203,W503
	@echo "✅ 代码检查完成"

# 创建示例PDF
create-sample:
	@echo "📄 创建示例PDF文件..."
	python -c "
import sys
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    import os
    os.makedirs('input', exist_ok=True)
    c = canvas.Canvas('input/sample.pdf', pagesize=letter)
    c.drawString(100, 750, 'OCRmyPDF Sample Document')
    c.drawString(100, 700, 'This is a test PDF for OCR processing.')
    c.drawString(100, 650, '测试中文文字识别功能')
    c.drawString(100, 600, 'English and Chinese mixed text')
    c.save()
    print('✅ 示例PDF创建成功: input/sample.pdf')
except ImportError:
    print('❌ 需要安装reportlab: pip install reportlab')
    sys.exit(1)
"

# 完整安装流程
install-all: setup docker-pull install-local dev-install
	@echo "🎉 完整安装完成!"
	@echo "运行 'make test' 测试功能"
