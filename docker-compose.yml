version: '3.8'

services:
  ocrmypdf:
    image: jbarlow83/ocrmypdf-alpine:latest
    container_name: ocrmypdf-processor
    volumes:
      - ./input:/data/input:ro
      - ./output:/data/output
      - ./temp:/tmp
    working_dir: /data
    user: "1000:1000"  # 根据需要调整用户ID
    environment:
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
    # 这个服务不会持续运行，只是为了方便管理镜像
    command: ["--version"]
    profiles:
      - manual  # 需要手动启动

  # Web服务示例（可选）
  ocrmypdf-web:
    image: jbarlow83/ocrmypdf-alpine:latest
    container_name: ocrmypdf-web
    ports:
      - "5000:5000"
    volumes:
      - ./input:/data/input
      - ./output:/data/output
      - ./temp:/tmp
    working_dir: /data
    environment:
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
    entrypoint: ["python"]
    command: ["webservice.py"]
    profiles:
      - web  # 需要手动启动web服务

volumes:
  temp:
    driver: local

networks:
  default:
    name: ocrmypdf-network
