#!/usr/bin/env python3
"""
OCRmyPDF 使用示例
演示如何使用Docker和本地方式进行PDF OCR识别
"""

import os
import sys
from pathlib import Path

def create_sample_directories():
    """创建示例目录结构"""
    directories = ["input", "output", "temp"]
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✅ 创建目录: {dir_name}/")

def docker_examples():
    """Docker方式示例"""
    print("\n🐳 Docker方式示例:")
    print("=" * 50)
    
    examples = [
        {
            "description": "1. 基本用法 - 中英文识别",
            "command": "python ocr_docker.py input/sample.pdf output/sample_ocr.pdf"
        },
        {
            "description": "2. 仅中文识别",
            "command": "python ocr_docker.py input/sample.pdf output/sample_chi.pdf -l chi_sim"
        },
        {
            "description": "3. 添加图像处理",
            "command": "python ocr_docker.py input/sample.pdf output/sample_enhanced.pdf --deskew --clean"
        },
        {
            "description": "4. 高质量优化",
            "command": "python ocr_docker.py input/sample.pdf output/sample_optimized.pdf --optimize 2"
        },
        {
            "description": "5. 拉取最新镜像",
            "command": "python ocr_docker.py input/sample.pdf output/sample.pdf --pull"
        }
    ]
    
    for example in examples:
        print(f"\n{example['description']}:")
        print(f"   {example['command']}")

def local_examples():
    """本地方式示例"""
    print("\n💻 本地方式示例:")
    print("=" * 50)
    
    examples = [
        {
            "description": "1. 检查依赖项",
            "command": "python ocr_local.py --check-deps"
        },
        {
            "description": "2. 查看可用语言",
            "command": "python ocr_local.py --list-languages"
        },
        {
            "description": "3. 基本OCR处理",
            "command": "python ocr_local.py input/sample.pdf output/sample_ocr.pdf"
        },
        {
            "description": "4. 批量处理",
            "command": "python ocr_local.py input/ output/ --batch"
        },
        {
            "description": "5. 高级选项",
            "command": "python ocr_local.py input/sample.pdf output/sample_advanced.pdf --deskew --clean --optimize 3"
        }
    ]
    
    for example in examples:
        print(f"\n{example['description']}:")
        print(f"   {example['command']}")

def batch_examples():
    """批量处理示例"""
    print("\n📁 批量处理示例:")
    print("=" * 50)
    
    examples = [
        {
            "description": "1. Docker批量处理",
            "command": "python batch_ocr.py input/ output/ -m docker"
        },
        {
            "description": "2. 本地批量处理",
            "command": "python batch_ocr.py input/ output/ -m local"
        },
        {
            "description": "3. 高并发处理",
            "command": "python batch_ocr.py input/ output/ -m docker -w 4"
        },
        {
            "description": "4. 指定语言和优化",
            "command": "python batch_ocr.py input/ output/ -m docker -l chi_sim --optimize 2"
        },
        {
            "description": "5. 扁平输出结构",
            "command": "python batch_ocr.py input/ output/ -m docker --flat-output"
        }
    ]
    
    for example in examples:
        print(f"\n{example['description']}:")
        print(f"   {example['command']}")

def docker_compose_examples():
    """Docker Compose示例"""
    print("\n🐙 Docker Compose示例:")
    print("=" * 50)
    
    examples = [
        {
            "description": "1. 拉取镜像",
            "command": "docker-compose pull"
        },
        {
            "description": "2. 运行OCR处理",
            "command": "docker-compose run --rm ocrmypdf --language chi_sim+eng /data/input/test.pdf /data/output/test_ocr.pdf"
        },
        {
            "description": "3. 启动Web服务",
            "command": "docker-compose --profile web up -d"
        },
        {
            "description": "4. 停止Web服务",
            "command": "docker-compose --profile web down"
        }
    ]
    
    for example in examples:
        print(f"\n{example['description']}:")
        print(f"   {example['command']}")

def installation_guide():
    """安装指南"""
    print("\n📦 安装指南:")
    print("=" * 50)
    
    print("\n🐳 Docker方式（推荐）:")
    print("1. 安装Docker: https://docs.docker.com/get-docker/")
    print("2. 拉取镜像: python ocr_docker.py --pull")
    print("3. 开始使用: python ocr_docker.py input.pdf output.pdf")
    
    print("\n💻 本地安装:")
    print("Ubuntu/Debian:")
    print("   sudo apt install tesseract-ocr tesseract-ocr-chi-sim ghostscript")
    print("   pip install -r requirements.txt")
    
    print("\nmacOS:")
    print("   brew install tesseract tesseract-lang ghostscript")
    print("   pip install -r requirements.txt")
    
    print("\nWindows:")
    print("   请参考OCRmyPDF官方文档进行安装")

def performance_tips():
    """性能建议"""
    print("\n⚡ 性能建议:")
    print("=" * 50)
    
    tips = [
        "1. Docker vs 本地: Docker更简单，本地性能更好",
        "2. 并发处理: 批量处理时使用 -w 参数调整并发数",
        "3. 内存管理: 大文件处理时减少并发数避免内存不足",
        "4. 语言选择: 只选择需要的语言提高速度和准确性",
        "5. 优化级别: 根据需要选择合适的优化级别 (0-3)",
        "6. 图像预处理: 使用 --deskew 和 --clean 提高识别质量"
    ]
    
    for tip in tips:
        print(f"   {tip}")

def main():
    """主函数"""
    print("🔍 OCRmyPDF PDF文字识别工具 - 使用示例")
    print("=" * 60)
    
    # 创建示例目录
    create_sample_directories()
    
    # 安装指南
    installation_guide()
    
    # 各种示例
    docker_examples()
    local_examples()
    batch_examples()
    docker_compose_examples()
    
    # 性能建议
    performance_tips()
    
    print("\n" + "=" * 60)
    print("📚 更多信息请查看 README.md 文件")
    print("🐛 遇到问题请检查故障排除部分")
    print("🤝 欢迎提交Issue和Pull Request!")

if __name__ == "__main__":
    main()
