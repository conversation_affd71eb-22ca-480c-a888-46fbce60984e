{"default_settings": {"language": "chi_sim+eng", "method": "docker", "optimize": 1, "deskew": false, "clean": false, "max_workers": 2, "output_type": "pdf"}, "docker_settings": {"image": "jbarlow83/ocrmypdf-alpine", "method": "stdin", "pull_on_start": false}, "local_settings": {"check_dependencies": true, "progress_bar": true}, "batch_settings": {"preserve_structure": true, "recursive": true, "skip_existing": false, "generate_report": true}, "supported_languages": {"chi_sim": "简体中文", "chi_tra": "繁体中文", "eng": "英文", "jpn": "日文", "kor": "韩文", "fra": "法文", "deu": "德文", "spa": "西班牙文", "rus": "俄文", "ara": "阿拉伯文"}, "optimization_levels": {"0": "不优化", "1": "无损优化（默认）", "2": "有损优化", "3": "激进优化"}, "file_patterns": {"input_extensions": [".pdf"], "output_prefix": "ocr_", "temp_dir": "temp", "output_dir": "output"}}