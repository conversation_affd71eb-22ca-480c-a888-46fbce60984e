#!/usr/bin/env python3
"""
OCRmyPDF 本地脚本
直接使用本地安装的OCRmyPDF进行PDF OCR识别
"""

import os
import sys
import argparse
from pathlib import Path
import logging

try:
    import ocrmypdf
except ImportError:
    print("错误: OCRmyPDF未安装")
    print("请运行: pip install ocrmypdf")
    print("或者使用Docker版本: python ocr_docker.py")
    sys.exit(1)


class OCRLocalProcessor:
    def __init__(self):
        """初始化本地OCR处理器"""
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def check_dependencies(self):
        """检查依赖项"""
        dependencies = {
            'tesseract': 'Tesseract OCR引擎',
            'ghostscript': 'Ghostscript PDF处理器'
        }
        
        missing = []
        for dep, desc in dependencies.items():
            if not self._check_command(dep):
                missing.append(f"{dep} ({desc})")
        
        if missing:
            print("缺少以下依赖项:")
            for dep in missing:
                print(f"  - {dep}")
            print("\n安装指南:")
            print("Ubuntu/Debian: sudo apt install tesseract-ocr ghostscript")
            print("CentOS/RHEL: sudo yum install tesseract ghostscript")
            print("macOS: brew install tesseract ghostscript")
            print("Windows: 请参考OCRmyPDF官方文档")
            return False
        
        return True
    
    def _check_command(self, command):
        """检查命令是否可用"""
        import shutil
        return shutil.which(command) is not None
    
    def get_available_languages(self):
        """获取可用的OCR语言"""
        try:
            from ocrmypdf import helpers
            languages = helpers.available_languages()
            return sorted(languages)
        except Exception as e:
            self.logger.warning(f"无法获取语言列表: {e}")
            return ["eng", "chi_sim", "chi_tra"]
    
    def ocr_pdf(self, input_path, output_path, language="chi_sim+eng", **kwargs):
        """
        执行PDF OCR处理
        
        Args:
            input_path: 输入PDF文件路径
            output_path: 输出PDF文件路径
            language: OCR语言
            **kwargs: 其他OCRmyPDF参数
        """
        input_path = Path(input_path)
        output_path = Path(output_path)
        
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_path}")
        
        # 创建输出目录
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"开始处理: {input_path}")
        self.logger.info(f"输出路径: {output_path}")
        self.logger.info(f"使用语言: {language}")
        
        try:
            # 设置默认参数
            ocr_options = {
                'language': language,
                'output_type': 'pdf',
                'progress_bar': True,
            }
            
            # 更新用户提供的参数
            ocr_options.update(kwargs)
            
            # 执行OCR
            result = ocrmypdf.ocr(
                input_file=input_path,
                output_file=output_path,
                **ocr_options
            )
            
            self.logger.info("OCR处理完成!")
            return True
            
        except ocrmypdf.exceptions.ExitCodeException as e:
            self.logger.error(f"OCR处理失败 (退出码 {e.exit_code}): {e}")
            return False
        except Exception as e:
            self.logger.error(f"处理过程中出错: {e}")
            return False
    
    def batch_ocr(self, input_dir, output_dir, language="chi_sim+eng", **kwargs):
        """
        批量处理PDF文件
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            language: OCR语言
            **kwargs: 其他OCRmyPDF参数
        """
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)
        
        if not input_dir.exists():
            raise FileNotFoundError(f"输入目录不存在: {input_dir}")
        
        # 创建输出目录
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 查找PDF文件
        pdf_files = list(input_dir.glob("*.pdf"))
        if not pdf_files:
            self.logger.warning(f"在 {input_dir} 中未找到PDF文件")
            return
        
        self.logger.info(f"找到 {len(pdf_files)} 个PDF文件")
        
        success_count = 0
        for pdf_file in pdf_files:
            output_file = output_dir / f"ocr_{pdf_file.name}"
            
            self.logger.info(f"处理 {pdf_file.name}...")
            if self.ocr_pdf(pdf_file, output_file, language, **kwargs):
                success_count += 1
            else:
                self.logger.error(f"处理失败: {pdf_file.name}")
        
        self.logger.info(f"批量处理完成: {success_count}/{len(pdf_files)} 成功")


def main():
    parser = argparse.ArgumentParser(description="使用本地OCRmyPDF进行PDF OCR识别")
    parser.add_argument("input", help="输入PDF文件或目录路径")
    parser.add_argument("output", help="输出PDF文件或目录路径")
    parser.add_argument("-l", "--language", default="chi_sim+eng", 
                       help="OCR语言 (默认: chi_sim+eng)")
    parser.add_argument("--batch", action="store_true", help="批量处理模式")
    parser.add_argument("--deskew", action="store_true", help="自动纠正倾斜")
    parser.add_argument("--clean", action="store_true", help="清理图像")
    parser.add_argument("--optimize", type=int, choices=[0, 1, 2, 3], default=1,
                       help="优化级别 (0-3, 默认: 1)")
    parser.add_argument("--check-deps", action="store_true", help="检查依赖项")
    parser.add_argument("--list-languages", action="store_true", help="列出可用语言")
    
    args = parser.parse_args()
    
    # 创建处理器
    processor = OCRLocalProcessor()
    
    # 检查依赖项
    if args.check_deps:
        if processor.check_dependencies():
            print("✅ 所有依赖项都已安装")
        sys.exit(0)
    
    # 列出可用语言
    if args.list_languages:
        languages = processor.get_available_languages()
        print("可用的OCR语言:")
        for lang in languages:
            print(f"  - {lang}")
        sys.exit(0)
    
    # 检查依赖项
    if not processor.check_dependencies():
        sys.exit(1)
    
    # 准备OCR参数
    ocr_kwargs = {
        "deskew": args.deskew,
        "clean": args.clean,
        "optimize": args.optimize
    }
    
    # 执行OCR处理
    try:
        if args.batch:
            processor.batch_ocr(args.input, args.output, args.language, **ocr_kwargs)
        else:
            success = processor.ocr_pdf(args.input, args.output, args.language, **ocr_kwargs)
            if success:
                print("✅ OCR处理成功完成!")
                sys.exit(0)
            else:
                print("❌ OCR处理失败!")
                sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
